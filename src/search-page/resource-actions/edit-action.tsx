import { ManageAppResourceForm } from '@/admin-page/ManageAppResource';
import { updateAppResource } from '@/api';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { useRootContext } from '@/context/useRootContext';
import { AppResource } from '@/core.types';
import {
  firebaseTimestampToString,
  formatDateToFirebaseTimestamp,
} from '@/services/utils-service';
import { Edit2 } from 'lucide-react';
import { useState } from 'react';
import { SubmitHandler } from 'react-hook-form';
import { useAppResourcesContext } from '../app-resources-context';

export const EditAction = ({ resource }: { resource: AppResource }) => {
  const [open, setOpen] = useState(false);
  const { hasAdminRole } = useRootContext();
  const { setResources } = useAppResourcesContext();

  const onSubmit: SubmitHandler<AppResource> = async (values) => {
    await updateAppResource(values);

    setResources((prev) =>
      prev.map((r) => {
        if (r.id === values.id) {
          return {
            ...values,
            createdAt: formatDateToFirebaseTimestamp(values.createdAt as any),
          };
        }
        return r;
      }),
    );
    setOpen(false);
  };

  const defaultValues: AppResource = {
    ...resource,
    createdAt: firebaseTimestampToString(resource.createdAt as any),
  };

  if (!hasAdminRole) return null;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant='ghost'
          size='icon'
          style={{
            cursor: 'pointer',
            borderRadius: '50%',
          }}
        >
          <Edit2 className='h-4 w-4' />
        </Button>
      </DialogTrigger>
      <DialogContent>
        <ManageAppResourceForm
          {...{
            onSubmit,
            defaultValues,
          }}
        />
      </DialogContent>
    </Dialog>
  );
};
