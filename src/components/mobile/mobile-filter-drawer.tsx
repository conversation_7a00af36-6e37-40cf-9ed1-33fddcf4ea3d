import { useState } from 'react';
import { Filter } from 'lucide-react';
import { Drawer } from 'vaul';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { AppAuthorSelect } from '@/components/AppSelect/AppAuthorSelect';
import { BookmarksToggle } from '@/components/BookmarksToggle';
import { SearchTags } from '@/search-page/search-tags';
import { useSearchFiltersContext } from '@/search-page/search-filters-context';
import { useAppResourcesContext } from '@/search-page/app-resources-context';
import { useRootContext } from '@/context/useRootContext';

interface MobileFilterDrawerProps {
  className?: string;
}

export const MobileFilterDrawer = ({ className }: MobileFilterDrawerProps) => {
  const [open, setOpen] = useState(false);
  const { authorId, setAuthorID } = useSearchFiltersContext();
  const { 
    resetAndReloadResources,
    bookmarksMode,
    showBookMarks,
    hideBookMarks 
  } = useAppResourcesContext();
  const { isAuthorized } = useRootContext();

  const onAuthorSelectChange = (authorId: string) => {
    setAuthorID(authorId);
    resetAndReloadResources();
  };

  const onBookmarkSwitchChange = (value: boolean) => {
    if (value) {
      showBookMarks();
    } else {
      hideBookMarks();
    }
  };

  return (
    <Drawer.Root open={open} onOpenChange={setOpen}>
      <Drawer.Trigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={cn(
            'h-9 px-3 border-2 hover:bg-primary/10 hover:border-primary/20 transition-colors',
            'border-slate-300 dark:border-slate-600',
            'hover:border-yellow-500 hover:text-yellow-600 dark:hover:text-yellow-400',
            className
          )}
        >
          <Filter className="h-4 w-4" />
        </Button>
      </Drawer.Trigger>
      
      <Drawer.Portal>
        <Drawer.Overlay className="fixed inset-0 bg-black/40" />
        <Drawer.Content className="bg-background flex flex-col rounded-t-[10px] h-[85vh] fixed bottom-0 left-0 right-0 outline-none">
          <div className="p-4 bg-background rounded-t-[10px] flex-1 overflow-hidden">
            <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-muted mb-6" />
            
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-center">Filters</h3>
              <p className="text-sm text-muted-foreground text-center mt-1">
                Refine your search results
              </p>
            </div>
            
            <div className="space-y-6 overflow-y-auto flex-1">
              {/* Author Selection */}
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
                  Author
                </h4>
                <AppAuthorSelect
                  value={authorId}
                  onChange={onAuthorSelectChange}
                />
              </div>

              {/* Bookmarks Toggle - only show if authorized */}
              {isAuthorized && (
                <div className="space-y-3">
                  <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
                    Bookmarks
                  </h4>
                  <BookmarksToggle
                    isActive={bookmarksMode}
                    onToggle={onBookmarkSwitchChange}
                    className="w-full"
                  />
                </div>
              )}

              {/* Tags Selection */}
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
                  Tags
                </h4>
                <div className="border rounded-lg p-3 bg-muted/20">
                  <SearchTags style={{ maxHeight: '300px', overflowY: 'auto' }} />
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4 border-t mt-4">
              <Button
                variant="outline"
                className="flex-1"
                onClick={() => setOpen(false)}
              >
                Close
              </Button>
              <Button
                className="flex-1"
                onClick={() => {
                  resetAndReloadResources();
                  setOpen(false);
                }}
              >
                Apply Filters
              </Button>
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
};
